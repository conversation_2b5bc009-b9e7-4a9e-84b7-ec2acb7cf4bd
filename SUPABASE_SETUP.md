# Configuration Supabase pour HCP CRM

## ✅ Configuration terminée

Votre projet Supabase est maintenant configuré avec les informations suivantes :
- **ID Projet** : `ropsllagusfbmgfvyzmc`
- **URL** : `https://ropsllagusfbmgfvyzmc.supabase.co`
- **Clé API anonyme** : Configurée dans `lib/services/supabase_config.dart`

## 🔧 Étapes de configuration dans Supabase

### 1. Créer les tables de base de données

1. Allez sur [supabase.com](https://supabase.com) et connectez-vous
2. Ouvrez votre projet `ropsllagusfbmgfvyzmc`
3. Allez dans **SQL Editor** (éditeur SQL)
4. Copiez tout le contenu du fichier `supabase_setup.sql`
5. Collez-le dans l'éditeur SQL et cliquez sur **Run** (Exécuter)

### 2. <PERSON><PERSON><PERSON> le bucket de stockage

1. Dans votre projet Supabase, allez dans **Storage**
2. Cliquez sur **New bucket**
3. Nommez-le : `hcp-crm-files`
4. Cochez **Public bucket** (pour permettre l'accès aux fichiers)
5. Cliquez sur **Create bucket**

### 3. Configurer les politiques de stockage (optionnel)

Pour permettre l'upload de fichiers, vous pouvez ajouter cette politique dans l'éditeur SQL :

```sql
-- Politique pour permettre l'upload de fichiers
CREATE POLICY "Allow anonymous uploads" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'hcp-crm-files');

-- Politique pour permettre le téléchargement de fichiers
CREATE POLICY "Allow anonymous downloads" ON storage.objects
    FOR SELECT USING (bucket_id = 'hcp-crm-files');
```

## 🧪 Tester la configuration

### 1. Lancer l'application

```bash
flutter run
```

### 2. Accéder à la page de test

Dans l'application, naviguez vers la page de test Supabase pour vérifier :
- ✅ Connexion à Supabase
- ✅ Migration des données existantes
- ✅ Fonctionnement du service hybride

### 3. Vérifier dans Supabase

Dans l'interface Supabase, vous devriez voir :
- **Table Editor** : Les tables créées avec les données migrées
- **Storage** : Le bucket `hcp-crm-files` créé

## 📊 Tables créées

| Table | Description | Colonnes principales |
|-------|-------------|---------------------|
| `categories` | Catégories de produits | id, name, default_price |
| `products` | Produits du catalogue | id, name, price, quantity, category_id |
| `invoices` | Factures et devis | id, client_name, total, status, type |
| `colis` | Colis et livraisons | id, libelle, statut, facture_id |
| `tasks` | Tâches et rappels | id, title, due_date, priority |

## 🔄 Migration des données

L'application migrera automatiquement vos données existantes de SharedPreferences vers Supabase lors de la première connexion.

### Données migrées :
- ✅ Factures
- ✅ Colis
- ✅ Produits
- ✅ Tâches
- ✅ Catégories

## 🛡️ Sécurité

### Politiques RLS configurées :
- Accès anonyme pour l'application mobile
- En production, implémentez une authentification appropriée

### Recommandations de sécurité :
1. **Authentification** : Ajoutez un système d'authentification utilisateur
2. **Politiques RLS** : Personnalisez les politiques selon vos besoins
3. **Clés API** : Gardez vos clés API secrètes
4. **Backup** : Configurez des sauvegardes automatiques

## 🚀 Fonctionnalités disponibles

### Service hybride
- ✅ Synchronisation automatique avec Supabase
- ✅ Fallback vers SharedPreferences si pas de connexion
- ✅ Migration transparente des données

### Stockage de fichiers
- ✅ Upload d'images de produits
- ✅ Upload de documents (factures, etc.)
- ✅ URLs signées pour le téléchargement

### Gestion des données
- ✅ CRUD complet pour toutes les entités
- ✅ Index optimisés pour les performances
- ✅ Mise à jour automatique des timestamps

## 🔧 Dépannage

### Problèmes courants :

1. **Erreur de connexion**
   - Vérifiez l'URL et la clé API dans `supabase_config.dart`
   - Vérifiez votre connexion internet

2. **Tables non créées**
   - Exécutez le script SQL dans l'éditeur Supabase
   - Vérifiez les logs d'erreur

3. **Migration échouée**
   - Vérifiez les données dans SharedPreferences
   - Relancez l'application

4. **Erreurs de stockage**
   - Vérifiez que le bucket `hcp-crm-files` est créé
   - Vérifiez les politiques de stockage

## 📞 Support

Si vous rencontrez des problèmes :
1. Vérifiez les logs de l'application
2. Consultez la documentation Supabase
3. Vérifiez la configuration dans `supabase_config.dart`

---

**Configuration terminée !** Votre application HCP CRM est maintenant connectée à Supabase et prête à synchroniser vos données. 🎉 