import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/invoice.dart';
import '../models/colis.dart';
import '../models/product.dart';
import '../models/task.dart';
import '../models/category.dart';
import 'supabase_config.dart';

/// Service principal pour gérer toutes les opérations Supabase
class SupabaseService {
  static final SupabaseClient _client = Supabase.instance.client;

  // ===== FACTURES =====

  /// Récupérer toutes les factures
  static Future<List<Invoice>> getInvoices() async {
    try {
      final response = await _client
          .from(SupabaseConfig.invoicesTable)
          .select()
          .order('created_at', ascending: false);

      return (response as List).map((json) => Invoice.fromJson(json)).toList();
    } catch (e) {
      print('Erreur lors de la récupération des factures: $e');
      rethrow;
    }
  }

  /// Ajouter une nouvelle facture
  static Future<void> addInvoice(Invoice invoice) async {
    try {
      await _client.from(SupabaseConfig.invoicesTable).insert(invoice.toJson());
    } catch (e) {
      print('Erreur lors de l\'ajout de la facture: $e');
      rethrow;
    }
  }

  /// Mettre à jour une facture
  static Future<void> updateInvoice(String id, Invoice invoice) async {
    try {
      await _client
          .from(SupabaseConfig.invoicesTable)
          .update(invoice.toJson())
          .eq('id', id);
    } catch (e) {
      print('Erreur lors de la mise à jour de la facture: $e');
      rethrow;
    }
  }

  /// Supprimer une facture
  static Future<void> deleteInvoice(String id) async {
    try {
      await _client.from(SupabaseConfig.invoicesTable).delete().eq('id', id);
    } catch (e) {
      print('Erreur lors de la suppression de la facture: $e');
      rethrow;
    }
  }

  // ===== COLIS =====

  /// Récupérer tous les colis
  static Future<List<Colis>> getColis() async {
    try {
      final response = await _client
          .from(SupabaseConfig.colisTable)
          .select()
          .order('created_at', ascending: false);

      return (response as List).map((json) => Colis.fromJson(json)).toList();
    } catch (e) {
      print('Erreur lors de la récupération des colis: $e');
      rethrow;
    }
  }

  /// Ajouter un nouveau colis
  static Future<void> addColis(Colis colis) async {
    try {
      await _client.from(SupabaseConfig.colisTable).insert(colis.toJson());
    } catch (e) {
      print('Erreur lors de l\'ajout du colis: $e');
      rethrow;
    }
  }

  /// Mettre à jour un colis
  static Future<void> updateColis(String id, Colis colis) async {
    try {
      await _client
          .from(SupabaseConfig.colisTable)
          .update(colis.toJson())
          .eq('id', id);
    } catch (e) {
      print('Erreur lors de la mise à jour du colis: $e');
      rethrow;
    }
  }

  /// Supprimer un colis
  static Future<void> deleteColis(String id) async {
    try {
      await _client.from(SupabaseConfig.colisTable).delete().eq('id', id);
    } catch (e) {
      print('Erreur lors de la suppression du colis: $e');
      rethrow;
    }
  }

  // ===== PRODUITS =====

  /// Récupérer tous les produits
  static Future<List<Product>> getProducts() async {
    try {
      final response = await _client
          .from(SupabaseConfig.productsTable)
          .select()
          .order('name', ascending: true);

      return (response as List).map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('Erreur lors de la récupération des produits: $e');
      rethrow;
    }
  }

  /// Ajouter un nouveau produit
  static Future<void> addProduct(Product product) async {
    try {
      await _client.from(SupabaseConfig.productsTable).insert(product.toJson());
    } catch (e) {
      print('Erreur lors de l\'ajout du produit: $e');
      rethrow;
    }
  }

  /// Mettre à jour un produit
  static Future<void> updateProduct(String id, Product product) async {
    try {
      await _client
          .from(SupabaseConfig.productsTable)
          .update(product.toJson())
          .eq('id', id);
    } catch (e) {
      print('Erreur lors de la mise à jour du produit: $e');
      rethrow;
    }
  }

  /// Supprimer un produit
  static Future<void> deleteProduct(String id) async {
    try {
      await _client.from(SupabaseConfig.productsTable).delete().eq('id', id);
    } catch (e) {
      print('Erreur lors de la suppression du produit: $e');
      rethrow;
    }
  }

  // ===== TÂCHES =====

  /// Récupérer toutes les tâches
  static Future<List<Task>> getTasks() async {
    try {
      final response = await _client
          .from(SupabaseConfig.tasksTable)
          .select()
          .order('created_at', ascending: false);

      return (response as List).map((json) => Task.fromJson(json)).toList();
    } catch (e) {
      print('Erreur lors de la récupération des tâches: $e');
      rethrow;
    }
  }

  /// Ajouter une nouvelle tâche
  static Future<void> addTask(Task task) async {
    try {
      await _client.from(SupabaseConfig.tasksTable).insert(task.toJson());
    } catch (e) {
      print('Erreur lors de l\'ajout de la tâche: $e');
      rethrow;
    }
  }

  /// Mettre à jour une tâche
  static Future<void> updateTask(String id, Task task) async {
    try {
      await _client
          .from(SupabaseConfig.tasksTable)
          .update(task.toJson())
          .eq('id', id);
    } catch (e) {
      print('Erreur lors de la mise à jour de la tâche: $e');
      rethrow;
    }
  }

  /// Supprimer une tâche
  static Future<void> deleteTask(String id) async {
    try {
      await _client.from(SupabaseConfig.tasksTable).delete().eq('id', id);
    } catch (e) {
      print('Erreur lors de la suppression de la tâche: $e');
      rethrow;
    }
  }

  // ===== CATÉGORIES =====

  /// Récupérer toutes les catégories
  static Future<List<Category>> getCategories() async {
    try {
      final response = await _client
          .from(SupabaseConfig.categoriesTable)
          .select()
          .order('name', ascending: true);

      return (response as List).map((json) => Category.fromJson(json)).toList();
    } catch (e) {
      print('Erreur lors de la récupération des catégories: $e');
      rethrow;
    }
  }

  /// Ajouter une nouvelle catégorie
  static Future<void> addCategory(Category category) async {
    try {
      await _client
          .from(SupabaseConfig.categoriesTable)
          .insert(category.toJson());
    } catch (e) {
      print('Erreur lors de l\'ajout de la catégorie: $e');
      rethrow;
    }
  }

  /// Mettre à jour une catégorie
  static Future<void> updateCategory(String id, Category category) async {
    try {
      await _client
          .from(SupabaseConfig.categoriesTable)
          .update(category.toJson())
          .eq('id', id);
    } catch (e) {
      print('Erreur lors de la mise à jour de la catégorie: $e');
      rethrow;
    }
  }

  /// Supprimer une catégorie
  static Future<void> deleteCategory(String id) async {
    try {
      await _client.from(SupabaseConfig.categoriesTable).delete().eq('id', id);
    } catch (e) {
      print('Erreur lors de la suppression de la catégorie: $e');
      rethrow;
    }
  }

  // ===== STOCKAGE DE FICHIERS =====

  /// Uploader une image
  static Future<String> uploadImage(String filePath, String fileName) async {
    try {
      final file = File(filePath);
      final response = await _client.storage
          .from(SupabaseConfig.storageBucket)
          .upload('images/$fileName', file);

      return response;
    } catch (e) {
      print('Erreur lors de l\'upload de l\'image: $e');
      rethrow;
    }
  }

  /// Télécharger une image
  static Future<String> downloadImage(String fileName) async {
    try {
      final response = await _client.storage
          .from(SupabaseConfig.storageBucket)
          .createSignedUrl('images/$fileName', 3600); // 1 heure

      return response;
    } catch (e) {
      print('Erreur lors du téléchargement de l\'image: $e');
      rethrow;
    }
  }

  /// Uploader un document
  static Future<String> uploadDocument(String filePath, String fileName) async {
    try {
      final file = File(filePath);
      final response = await _client.storage
          .from(SupabaseConfig.storageBucket)
          .upload('documents/$fileName', file);

      return response;
    } catch (e) {
      print('Erreur lors de l\'upload du document: $e');
      rethrow;
    }
  }

  /// Télécharger un document
  static Future<String> downloadDocument(String fileName) async {
    try {
      final response = await _client.storage
          .from(SupabaseConfig.storageBucket)
          .createSignedUrl('documents/$fileName', 3600); // 1 heure

      return response;
    } catch (e) {
      print('Erreur lors du téléchargement du document: $e');
      rethrow;
    }
  }

  // ===== TESTS DE CONNEXION =====

  /// Tester la connexion Supabase
  static Future<bool> testConnection() async {
    try {
      await _client.from(SupabaseConfig.invoicesTable).select('id').limit(1);
      return true;
    } catch (e) {
      print('Erreur de connexion Supabase: $e');
      return false;
    }
  }
}
