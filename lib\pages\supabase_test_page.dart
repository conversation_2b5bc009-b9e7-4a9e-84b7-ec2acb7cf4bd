import 'package:flutter/material.dart';
import '../services/supabase_migration_service.dart';
import '../services/hybrid_supabase_service.dart';
import '../constants/app_colors.dart';

class SupabaseTestPage extends StatefulWidget {
  const SupabaseTestPage({super.key});

  @override
  State<SupabaseTestPage> createState() => _SupabaseTestPageState();
}

class _SupabaseTestPageState extends State<SupabaseTestPage> {
  bool _isLoading = false;
  bool _isConnected = false;
  String _statusMessage = '';
  String _migrationStatus = '';

  @override
  void initState() {
    super.initState();
    _testConnection();
  }

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Test de connexion en cours...';
    });

    try {
      final isConnected = await SupabaseMigrationService.testConnection();
      setState(() {
        _isConnected = isConnected;
        _statusMessage =
            isConnected
                ? '✅ Connexion Supabase réussie!'
                : '❌ Échec de la connexion Supabase';
      });
    } catch (e) {
      setState(() {
        _isConnected = false;
        _statusMessage = '❌ Erreur de connexion: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _migrateData() async {
    setState(() {
      _isLoading = true;
      _migrationStatus = 'Migration des données en cours...';
    });

    try {
      await SupabaseMigrationService.migrateAll();
      setState(() {
        _migrationStatus = '✅ Migration terminée avec succès!';
      });
    } catch (e) {
      setState(() {
        _migrationStatus = '❌ Erreur lors de la migration: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _initializeHybridService() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Initialisation du service hybride...';
    });

    try {
      await HybridSupabaseService.initialize();
      final isUsingSupabase = HybridSupabaseService.isUsingSupabase;
      setState(() {
        _statusMessage =
            isUsingSupabase
                ? '✅ Service hybride initialisé - Utilisation de Supabase'
                : '⚠️ Service hybride initialisé - Utilisation de SharedPreferences';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '❌ Erreur d\'initialisation: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Supabase'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Statut de la connexion',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _isConnected ? Icons.check_circle : Icons.error,
                          color: _isConnected ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Expanded(child: Text(_statusMessage)),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _testConnection,
                      child: const Text('Tester la connexion'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Migration des données',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _migrationStatus.isEmpty
                          ? 'Aucune migration effectuée'
                          : _migrationStatus,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed:
                          (_isLoading || !_isConnected) ? null : _migrateData,
                      child: const Text('Migrer les données'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Service hybride',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _initializeHybridService,
                      child: const Text('Initialiser le service hybride'),
                    ),
                  ],
                ),
              ),
            ),
            const Spacer(),
            if (_isLoading) const Center(child: CircularProgressIndicator()),
          ],
        ),
      ),
    );
  }
}
