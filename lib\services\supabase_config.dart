/// Configuration Supabase pour HCP CRM
class SupabaseConfig {
  // URL et clé API Supabase
  static const String url = 'https://ropsllagusfbmgfvyzmc.supabase.co';
  static const String anonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvcHNsbGFndXNmYm1nZnZ5em1jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxOTQwMjAsImV4cCI6MjA2Nzc3MDAyMH0.xH_Ldje3JJVkk-yS009U0Z5St7ycrA0icOjaFCghpjA';

  // Noms des tables dans Supabase
  static const String invoicesTable = 'invoices';
  static const String colisTable = 'colis';
  static const String productsTable = 'products';
  static const String tasksTable = 'tasks';
  static const String categoriesTable = 'categories';

  // Bucket pour les fichiers
  static const String storageBucket = 'hcp-crm-files';
}
