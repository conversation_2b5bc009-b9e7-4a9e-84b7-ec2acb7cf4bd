import 'dart:io';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import '../models/invoice.dart';
import '../models/colis.dart';
import 'dart:math' as math;

class PDFService {
  static final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');
  static final DateFormat _dateFormat = DateFormat('dd/MM/yyyy', 'fr_FR');

  // Cache pour le logo
  static pw.MemoryImage? _logoImage;

  // Formate un montant sans espace insécable
  static String formatMontant(num value) {
    return _currencyFormat.format(value).replaceAll('\u00A0', ' ');
  }

  // Charge le logo depuis les assets
  static Future<pw.MemoryImage?> _loadLogo() async {
    if (_logoImage != null) return _logoImage;

    try {
      final ByteData data = await rootBundle.load(
        'assets/images/logo_entreprise.png',
      );
      final Uint8List bytes = data.buffer.asUint8List();
      _logoImage = pw.MemoryImage(bytes);
      return _logoImage;
    } catch (e) {
      // Erreur lors du chargement du logo - utiliser le fallback
      return null;
    }
  }

  // Méthode pour générer le PDF sans le sauvegarder (pour prévisualisation)
  static Future<pw.Document> generateInvoicePDF(Invoice invoice) async {
    final pdf = pw.Document();

    // Charger le header en premier car il est async
    final header = await _buildHeader();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            header,
            pw.SizedBox(height: 30),
            _buildInvoiceInfo(invoice),
            pw.SizedBox(height: 20),
            _buildClientInfo(invoice),
            pw.SizedBox(height: 30),
            _buildItemsTable(invoice),
            pw.SizedBox(height: 20),
            _buildTotalSection(invoice, context), // Passer le context ici
            if (invoice.type == InvoiceType.proforma &&
                invoice.specialConditions != null &&
                invoice.specialConditions!.isNotEmpty)
              pw.SizedBox(height: 20),
            if (invoice.type == InvoiceType.proforma &&
                invoice.specialConditions != null &&
                invoice.specialConditions!.isNotEmpty)
              _buildSpecialConditionsSection(invoice.specialConditions!),
            if (invoice.notes != null && invoice.notes!.isNotEmpty)
              pw.SizedBox(height: 20),
            if (invoice.notes != null && invoice.notes!.isNotEmpty)
              _buildNotesSection(invoice.notes!),
            pw.SizedBox(height: 30),
            _buildFooter(invoice.footerNote),
          ];
        },
      ),
    );

    return pdf;
  }

  static Future<pw.Widget> _buildHeader() async {
    // Charger le logo depuis les assets
    pw.ImageProvider? logoImage;
    try {
      final logoData = await rootBundle.load(
        'assets/images/logo_entreprise.png',
      );
      logoImage = pw.MemoryImage(logoData.buffer.asUint8List());
    } catch (e) {
      // Si le logo n'est pas trouvé, on continue sans logo
      logoImage = null;
    }

    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue900,
        borderRadius: pw.BorderRadius.circular(10),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'HCP-DESIGN',
                style: pw.TextStyle(
                  fontSize: 28,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.white,
                ),
              ),
              if (logoImage != null)
                pw.Container(
                  width: 80, // Agrandissement du logo
                  height: 80, // Agrandissement du logo
                  child: pw.Image(logoImage, fit: pw.BoxFit.contain),
                ),
            ],
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Téléphone: +225 07 09 49 58 48',
                    style: const pw.TextStyle(
                      fontSize: 12,
                      color: PdfColors.white,
                    ),
                  ),
                  pw.Text(
                    'Email: <EMAIL>',
                    style: const pw.TextStyle(
                      fontSize: 12,
                      color: PdfColors.white,
                    ),
                  ),
                ],
              ),
              pw.Text(
                'www.hcp-designci.com',
                style: const pw.TextStyle(fontSize: 12, color: PdfColors.white),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildInvoiceInfo(Invoice invoice) {
    final isProforma = invoice.type == InvoiceType.proforma;

    return pw.Column(
      children: [
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  isProforma ? 'FACTURE PROFORMA' : 'FACTURE',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    color: isProforma ? PdfColors.orange800 : PdfColors.blue900,
                  ),
                ),
                if (isProforma)
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(top: 4),
                    child: pw.Text(
                      'Cette facture proforma ne vaut pas facture définitive',
                      style: pw.TextStyle(
                        fontSize: 10,
                        fontStyle: pw.FontStyle.italic,
                        color: PdfColors.orange600,
                      ),
                    ),
                  ),
                pw.SizedBox(height: 5),
                pw.Text(
                  'N° ${invoice.invoiceNumber}',
                  style: const pw.TextStyle(
                    fontSize: 14,
                    color: PdfColors.grey700,
                  ),
                ),
              ],
            ),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  'Date: ${_dateFormat.format(invoice.createdAt)}',
                  style: const pw.TextStyle(fontSize: 12),
                ),
                if (isProforma && invoice.validityDate != null)
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(top: 5),
                    child: pw.Text(
                      'Valide jusqu\'au: ${_dateFormat.format(invoice.validityDate!)}',
                      style: pw.TextStyle(
                        fontSize: 12,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.orange700,
                      ),
                    ),
                  ),
                if (invoice.deliveryDetails != null &&
                    invoice.deliveryDetails!.isNotEmpty)
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(top: 5),
                    child: pw.Text(
                      'Détails livraison: ${invoice.deliveryDetails}',
                      style: const pw.TextStyle(
                        fontSize: 10,
                        color: PdfColors.grey700,
                      ),
                    ),
                  ),
                pw.SizedBox(height: 5),
                pw.Container(
                  padding: const pw.EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: pw.BoxDecoration(
                    color: _getStatusColor(invoice.status),
                    borderRadius: pw.BorderRadius.circular(4),
                  ),
                  child: pw.Text(
                    invoice.status.displayName,
                    style: const pw.TextStyle(
                      fontSize: 10,
                      color: PdfColors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        // Informations spécifiques aux factures proforma
        if (isProforma) ..._buildProformaSpecificInfo(invoice),
      ],
    );
  }

  static List<pw.Widget> _buildProformaSpecificInfo(Invoice invoice) {
    final widgets = <pw.Widget>[];

    if (invoice.companyRccm != null && invoice.companyRccm!.isNotEmpty ||
        invoice.companyTaxNumber != null &&
            invoice.companyTaxNumber!.isNotEmpty) {
      widgets.add(pw.SizedBox(height: 15));
      widgets.add(
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            color: PdfColors.orange50,
            border: pw.Border.all(color: PdfColors.orange200),
            borderRadius: pw.BorderRadius.circular(6),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'INFORMATIONS ENTREPRISE',
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.orange800,
                ),
              ),
              pw.SizedBox(height: 6),
              if (invoice.companyRccm != null &&
                  invoice.companyRccm!.isNotEmpty)
                pw.Text(
                  'N° RCCM: ${invoice.companyRccm}',
                  style: const pw.TextStyle(fontSize: 10),
                ),
              if (invoice.companyTaxNumber != null &&
                  invoice.companyTaxNumber!.isNotEmpty)
                pw.Text(
                  'N° Contribuable: ${invoice.companyTaxNumber}',
                  style: const pw.TextStyle(fontSize: 10),
                ),
            ],
          ),
        ),
      );
    }

    if (invoice.paymentMethods != null && invoice.paymentMethods!.isNotEmpty) {
      widgets.add(pw.SizedBox(height: 15));
      widgets.add(
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            color: PdfColors.blue50,
            border: pw.Border.all(color: PdfColors.blue200),
            borderRadius: pw.BorderRadius.circular(6),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'MODES DE PAIEMENT ACCEPTÉS',
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 6),
              pw.Text(
                invoice.paymentMethods!.join(' • '),
                style: const pw.TextStyle(fontSize: 10),
              ),
            ],
          ),
        ),
      );
    }

    return widgets;
  }

  static pw.Widget _buildClientInfo(Invoice invoice) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'INFORMATIONS CLIENT',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue900,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            'Nom: ${invoice.clientName}',
            style: const pw.TextStyle(fontSize: 12),
          ),
          pw.Text(
            'Téléphone: ${invoice.clientNumber}',
            style: const pw.TextStyle(fontSize: 12),
          ),
          if (invoice.clientAddress != null &&
              invoice.clientAddress!.isNotEmpty)
            pw.Text(
              'Adresse: ${invoice.clientAddress}',
              style: const pw.TextStyle(fontSize: 12),
            ),
          if (invoice.clientEmail != null && invoice.clientEmail!.isNotEmpty)
            pw.Text(
              'Email: ${invoice.clientEmail}',
              style: const pw.TextStyle(fontSize: 12),
            ),
          pw.Text(
            'Description: ${invoice.products}',
            style: const pw.TextStyle(fontSize: 12),
          ),
          pw.Text(
            'Zone de livraison: ${invoice.deliveryLocation}',
            style: const pw.TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildItemsTable(Invoice invoice) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'DÉTAIL DES ARTICLES',
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue900,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(3),
            1: const pw.FlexColumnWidth(1),
            2: const pw.FlexColumnWidth(2),
            3: const pw.FlexColumnWidth(2),
          },
          children: [
            // En-tête du tableau
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('Article', isHeader: true),
                _buildTableCell('Qté', isHeader: true),
                _buildTableCell('Prix unitaire', isHeader: true),
                _buildTableCell('Total', isHeader: true),
              ],
            ),
            // Lignes des articles
            ...invoice.items.map(
              (item) => pw.TableRow(
                children: [
                  _buildTableCell(item.name),
                  _buildTableCell(item.quantity.toString()),
                  _buildTableCell('${_currencyFormat.format(item.price)} FCFA'),
                  _buildTableCell('${_currencyFormat.format(item.total)} FCFA'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }

  static pw.Widget _buildTotalSection(Invoice invoice, pw.Context context) {
    return pw.Container(
      width: double.infinity,
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.end,
        children: [
          pw.Container(
            width: 250,
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey300),
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                _buildTotalRow('Sous-total', invoice.subtotal, context),
                _buildTotalRow('Livraison', invoice.deliveryPrice, context),
                if (invoice.discountAmount >
                    0) // Ajout de l'affichage de la remise
                  _buildTotalRow(
                    'Remise',
                    -invoice.discountAmount,
                    context,
                    isDiscount: true,
                  ),
                if (invoice.advance > 0)
                  _buildTotalRow(
                    'Avance',
                    -invoice.advance,
                    context,
                    isAdvance: true,
                  ),
                pw.Divider(color: PdfColors.grey400, height: 20),
                _buildTotalRow('TOTAL', invoice.total, context, isTotal: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildTotalRow(
    String label,
    double value,
    pw.Context context, {
    bool isTotal = false,
    bool isAdvance = false,
    bool isDiscount = false,
  }) {
    final PdfColor textColor =
        isAdvance
            ? PdfColors.orange700
            : isDiscount
            ? PdfColors.green700
            : PdfColors.black;
    final style =
        isTotal
            ? pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              fontSize: 14,
              color: PdfColors.blue900,
            )
            : pw.TextStyle(fontSize: 12, color: textColor);
    final valueStyle =
        isTotal
            ? pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              fontSize: 14,
              color: PdfColors.blue900,
            )
            : pw.TextStyle(
              fontSize: 12,
              color: textColor,
              fontWeight: pw.FontWeight.bold,
            );

    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(label, style: style),
          pw.Text(
            '${isDiscount ? '' : ''}${_currencyFormat.format(value)} FCFA',
            style: valueStyle,
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildSpecialConditionsSection(String specialConditions) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.orange50,
        border: pw.Border.all(color: PdfColors.orange300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'CONDITIONS SPÉCIALES',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.orange800,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(specialConditions, style: const pw.TextStyle(fontSize: 10)),
        ],
      ),
    );
  }

  static pw.Widget _buildNotesSection(String notes) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'NOTES',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue900,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(notes, style: const pw.TextStyle(fontSize: 10)),
        ],
      ),
    );
  }

  static pw.Widget _buildFooter(String? customFooterNote) {
    final footerText =
        customFooterNote?.isNotEmpty == true
            ? customFooterNote!
            : 'Merci pour votre confiance - HCP-DESIGN';

    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: const pw.BoxDecoration(color: PdfColors.grey100),
      child: pw.Center(
        child: pw.Text(
          footerText,
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue900,
          ),
        ),
      ),
    );
  }

  static PdfColor _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.payee:
        return PdfColors.green;

      case InvoiceStatus.enAttente:
        return PdfColors.orange;
      case InvoiceStatus.annulee:
        return PdfColors.red;
    }
  }

  // Méthode pour générer un PDF groupé sans le sauvegarder (pour prévisualisation)
  static Future<pw.Document> generateGroupedReceiptDocument(
    List<Invoice> invoices,
  ) async {
    final pdf = pw.Document();

    // Vérifier que nous avons des factures valides
    if (invoices.isEmpty) {
      throw Exception('Aucune facture fournie pour la génération du PDF');
    }

    // Charger le logo
    final logo = await _loadLogo();

    // Calculer le nombre de factures par page (max 6)
    // Les factures seront affichées dans une grille 3x2

    // Générer les pages avec les factures carrées (format simple)
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          // Créer uniquement la grille de factures carrées
          return _buildSquareInvoicesGrid(invoices, logo);
        },
      ),
    );

    return pdf;
  }

  // Méthode de test avec des données fixes
  static Future<pw.Document> generateTestSquareInvoicesDocument() async {
    // Créer des factures de test avec des données fixes
    final testInvoices = [
      Invoice(
        id: 'test1',
        clientName: 'Kouakou Arnaud',
        clientNumber: '07 18 54 78 52',
        deliveryLocation: 'Cocody angré',
        products: 'Test produit',
        items: [],
        deliveryPrice: 0,
        advance: 0,
        subtotal: 10000,
        total: 10000,
        status: InvoiceStatus.enAttente,
        createdAt: DateTime.now(),
      ),
      Invoice(
        id: 'test2',
        clientName: 'Alex biji',
        clientNumber: '07 02 45 85 47',
        deliveryLocation: 'Plateau',
        products: 'Test produit 2',
        items: [],
        deliveryPrice: 0,
        advance: 0,
        subtotal: 3000,
        total: 3000,
        status: InvoiceStatus.enAttente,
        createdAt: DateTime.now(),
      ),
    ];

    return generateGroupedReceiptDocument(testInvoices);
  }

  // Méthode utilitaire pour valider et nettoyer le texte
  static String _validateAndCleanText(String? text, String fallback) {
    if (text == null || text.trim().isEmpty) {
      return fallback;
    }
    return text.trim();
  }

  // Méthode pour enrichir les données d'une facture si nécessaire
  static Invoice _enrichInvoiceData(Invoice invoice) {
    // Si les informations de base sont manquantes, essayer de les récupérer
    // depuis d'autres champs ou utiliser des valeurs par défaut intelligentes

    String clientName = invoice.clientName.trim();
    String clientNumber = invoice.clientNumber.trim();
    String deliveryLocation = invoice.deliveryLocation.trim();

    // Si le nom du client est vide, essayer d'utiliser l'ID comme fallback
    if (clientName.isEmpty) {
      clientName = 'Client ${invoice.id.substring(0, 8)}';
    }

    // Si le numéro est vide, utiliser un placeholder
    if (clientNumber.isEmpty) {
      clientNumber = 'Non renseigné';
    }

    // Si le lieu de livraison est vide, utiliser un placeholder
    if (deliveryLocation.isEmpty) {
      deliveryLocation = 'À définir';
    }

    // Retourner la facture avec les données enrichies si nécessaire
    if (clientName != invoice.clientName ||
        clientNumber != invoice.clientNumber ||
        deliveryLocation != invoice.deliveryLocation) {
      return invoice.copyWith(
        clientName: clientName,
        clientNumber: clientNumber,
        deliveryLocation: deliveryLocation,
      );
    }

    return invoice;
  }

  static pw.Widget _buildSquareInvoicesGrid(
    List<Invoice> invoices,
    pw.MemoryImage? logo,
  ) {
    // Définir le nombre maximum de factures par page
    final int maxInvoicesPerPage = 6;
    final int totalInvoices = invoices.length;
    final int emptySlots = math.max(0, maxInvoicesPerPage - totalInvoices);

    // Calculer la disposition de la grille (3 colonnes, 2 lignes)
    final int columns = 3;
    final int rows = 2;

    // Créer une liste de widgets pour toutes les factures et les emplacements vides
    List<pw.Widget> invoiceWidgets = [];

    // Ajouter les factures existantes avec enrichissement des données
    for (int i = 0; i < totalInvoices; i++) {
      final enrichedInvoice = _enrichInvoiceData(invoices[i]);
      invoiceWidgets.add(_buildSquareInvoice(enrichedInvoice, logo));
    }

    // Ajouter les emplacements vides
    for (int i = 0; i < emptySlots; i++) {
      invoiceWidgets.add(_buildEmptySquareInvoice(logo));
    }

    // Créer des lignes de grille
    List<pw.Widget> gridRows = [];
    for (int row = 0; row < rows; row++) {
      List<pw.Widget> rowChildren = [];
      for (int col = 0; col < columns; col++) {
        final int index = row * columns + col;
        if (index < invoiceWidgets.length) {
          rowChildren.add(
            pw.Expanded(
              child: pw.Padding(
                padding: const pw.EdgeInsets.all(5),
                child: invoiceWidgets[index],
              ),
            ),
          );
        }
      }

      gridRows.add(
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: rowChildren,
        ),
      );

      // Ajouter un espace entre les lignes
      if (row < rows - 1) {
        gridRows.add(pw.SizedBox(height: 10));
      }
    }

    return pw.Column(children: gridRows);
  }

  static pw.Widget _buildSquareInvoice(Invoice invoice, pw.MemoryImage? logo) {
    // Validation et nettoyage des données
    final clientName = _validateAndCleanText(
      invoice.clientName,
      'Client non renseigné',
    );
    final clientNumber = _validateAndCleanText(
      invoice.clientNumber,
      'Numéro non renseigné',
    );
    final deliveryLocation = _validateAndCleanText(
      invoice.deliveryLocation,
      'Lieu non renseigné',
    );

    // Calculer le montant à payer (total - avance)
    final montantAPayer = invoice.total - invoice.advance;

    return pw.Container(
      height: 280, // Hauteur augmentée pour le nouveau format
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black, width: 3),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(15)),
      ),
      child: pw.Padding(
        padding: const pw.EdgeInsets.all(12),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            // En-tête HCP-DESIGN avec bordure arrondie (comme dans l'image)
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.symmetric(
                vertical: 8,
                horizontal: 12,
              ),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.black, width: 2),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(10)),
              ),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  // Logo HCP à gauche avec bordure
                  pw.Container(
                    padding: const pw.EdgeInsets.all(6),
                    decoration: pw.BoxDecoration(
                      border: pw.Border.all(color: PdfColors.black, width: 1.5),
                      borderRadius: const pw.BorderRadius.all(
                        pw.Radius.circular(8),
                      ),
                    ),
                    child: pw.Column(
                      children: [
                        pw.Text(
                          'HCP',
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.Text(
                          'DESIGN',
                          style: pw.TextStyle(
                            fontSize: 8,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Informations de contact à droite
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Row(
                        mainAxisSize: pw.MainAxisSize.min,
                        children: [
                          pw.Text('📱 ', style: pw.TextStyle(fontSize: 8)),
                          pw.Text(
                            '07-09-49-58-48 / 01-53-11-74-55',
                            style: pw.TextStyle(
                              fontSize: 8,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      pw.Row(
                        mainAxisSize: pw.MainAxisSize.min,
                        children: [
                          pw.Text('📘 ', style: pw.TextStyle(fontSize: 8)),
                          pw.Text(
                            'HCP-DESIGN',
                            style: pw.TextStyle(
                              fontSize: 10,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      pw.Text(
                        'www.hcp-designci.com',
                        style: pw.TextStyle(
                          fontSize: 8,
                          color: PdfColors.blue,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 15),

            // Nom du client (exactement comme dans l'image)
            pw.Text(
              'Nom du client',
              style: pw.TextStyle(
                fontSize: 12,
                decoration: pw.TextDecoration.underline,
                fontWeight: pw.FontWeight.normal,
              ),
            ),
            pw.SizedBox(height: 4),
            pw.Text(
              clientName,
              style: pw.TextStyle(
                fontSize: 18,
                fontWeight: pw.FontWeight.bold,
                color:
                    clientName != 'Client non renseigné'
                        ? PdfColors.black
                        : PdfColors.red,
              ),
            ),

            pw.SizedBox(height: 12),

            // Numéro du client (exactement comme dans l'image)
            pw.Text(
              'Numéro du client',
              style: pw.TextStyle(
                fontSize: 12,
                decoration: pw.TextDecoration.underline,
                fontWeight: pw.FontWeight.normal,
              ),
            ),
            pw.SizedBox(height: 4),
            pw.Text(
              clientNumber,
              style: pw.TextStyle(
                fontSize: 18,
                fontWeight: pw.FontWeight.bold,
                color:
                    clientNumber != 'Numéro non renseigné'
                        ? PdfColors.black
                        : PdfColors.red,
              ),
            ),

            pw.SizedBox(height: 12),

            // Lieu de livraison (exactement comme dans l'image)
            pw.Text(
              'Lieu de livraison',
              style: pw.TextStyle(
                fontSize: 12,
                decoration: pw.TextDecoration.underline,
                fontWeight: pw.FontWeight.normal,
              ),
            ),
            pw.SizedBox(height: 4),
            pw.Text(
              deliveryLocation,
              style: pw.TextStyle(
                fontSize: 18,
                fontWeight: pw.FontWeight.bold,
                color:
                    deliveryLocation != 'Lieu non renseigné'
                        ? PdfColors.black
                        : PdfColors.red,
              ),
            ),

            pw.Spacer(),

            // Montant à payer (exactement comme dans l'image avec encadré gris)
            pw.Container(
              width: double.infinity,
              decoration: pw.BoxDecoration(
                color: PdfColors.grey300,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(12)),
                border: pw.Border.all(color: PdfColors.black, width: 1),
              ),
              padding: const pw.EdgeInsets.symmetric(
                vertical: 12,
                horizontal: 16,
              ),
              child: pw.Column(
                children: [
                  pw.Text(
                    'Montant à payer',
                    style: pw.TextStyle(
                      fontSize: 12,
                      decoration: pw.TextDecoration.underline,
                      fontWeight: pw.FontWeight.normal,
                    ),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Text(
                    '${NumberFormat('#,###').format(montantAPayer)} fr CFA',
                    style: pw.TextStyle(
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  static pw.Widget _buildEmptySquareInvoice(pw.MemoryImage? logo) {
    return pw.Container(
      height: 280, // Même hauteur que les nouveaux reçus
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black, width: 3),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(15)),
      ),
      child: pw.Padding(
        padding: const pw.EdgeInsets.all(8),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header avec informations de l'entreprise HCP-DESIGN (même que les factures pleines)
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                // Logo HCP gauche
                pw.Row(
                  children: [
                    // Logo réel ou fallback
                    logo != null
                        ? pw.Container(
                          width: 18,
                          height: 18,
                          child: pw.Image(logo, fit: pw.BoxFit.contain),
                        )
                        : pw.Container(
                          width: 18,
                          height: 18,
                          decoration: pw.BoxDecoration(
                            color: PdfColors.blue,
                            shape: pw.BoxShape.circle,
                          ),
                          child: pw.Center(
                            child: pw.Text(
                              'C',
                              style: pw.TextStyle(
                                color: PdfColors.white,
                                fontSize: 9,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                    pw.SizedBox(width: 2),
                    pw.Text(
                      'HCP',
                      style: pw.TextStyle(
                        fontSize: 12,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                // Informations entreprise droite
                pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Row(
                      mainAxisSize: pw.MainAxisSize.min,
                      children: [
                        // Logo réel ou fallback (plus petit)
                        logo != null
                            ? pw.Container(
                              width: 10,
                              height: 10,
                              child: pw.Image(logo, fit: pw.BoxFit.contain),
                            )
                            : pw.Container(
                              width: 10,
                              height: 10,
                              decoration: pw.BoxDecoration(
                                color: PdfColors.blue,
                                shape: pw.BoxShape.circle,
                              ),
                              child: pw.Center(
                                child: pw.Text(
                                  'C',
                                  style: pw.TextStyle(
                                    color: PdfColors.white,
                                    fontSize: 5,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                        pw.SizedBox(width: 1),
                        pw.Text(
                          'Habillages et',
                          style: pw.TextStyle(fontSize: 6),
                        ),
                      ],
                    ),
                    pw.Text('Coque', style: pw.TextStyle(fontSize: 6)),
                    pw.Text('Personnalisés', style: pw.TextStyle(fontSize: 6)),
                    pw.Text(
                      '07 09 49 58 48',
                      style: pw.TextStyle(
                        fontSize: 6,
                        color: PdfColors.green,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      'www.hcp-designci.com',
                      style: pw.TextStyle(fontSize: 5, color: PdfColors.blue),
                    ),
                  ],
                ),
              ],
            ),

            pw.SizedBox(height: 6),

            // Espaces vides pour les informations
            pw.Text(
              'Nom du client',
              style: pw.TextStyle(
                fontSize: 8,
                color: PdfColors.grey600,
                decoration: pw.TextDecoration.underline,
              ),
            ),
            pw.SizedBox(height: 14),

            pw.Text(
              'Numéro du client',
              style: pw.TextStyle(
                fontSize: 8,
                color: PdfColors.grey600,
                decoration: pw.TextDecoration.underline,
              ),
            ),
            pw.SizedBox(height: 14),

            pw.Text(
              'Lieu de livraison',
              style: pw.TextStyle(
                fontSize: 8,
                color: PdfColors.grey600,
                decoration: pw.TextDecoration.underline,
              ),
            ),

            pw.Spacer(),

            // Reste à payer vide
            pw.Container(
              width: double.infinity,
              decoration: pw.BoxDecoration(
                color: PdfColors.grey400,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(6)),
              ),
              padding: const pw.EdgeInsets.symmetric(
                vertical: 6,
                horizontal: 8,
              ),
              child: pw.Center(
                child: pw.Text(
                  'Reste à payer',
                  style: pw.TextStyle(
                    fontSize: 10,
                    color: PdfColors.grey700,
                    decoration: pw.TextDecoration.underline,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Méthode pour générer le PDF des livraisons sans le sauvegarder (pour prévisualisation)
  static Future<pw.Document> generateDeliveryListDocument(
    List<dynamic> deliveries,
    DateTime date,
  ) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          return [
            // En-tête avec logo HCP
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                // Logo HCP (simulé avec du texte stylisé)
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.black, width: 2),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'HCP',
                        style: pw.TextStyle(
                          fontSize: 24,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        'DESIGN',
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        '07 09 49 58 48',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                      pw.Text(
                        'www.hcp-designci.com',
                        style: const pw.TextStyle(fontSize: 8),
                      ),
                    ],
                  ),
                ),
                // Date de livraison
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.black, width: 2),
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'Date de livraison',
                        style: pw.TextStyle(
                          fontSize: 14,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        DateFormat('dd - MM - yyyy').format(date),
                        style: pw.TextStyle(
                          fontSize: 24,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            pw.SizedBox(height: 30),
            _buildDeliveryTable(deliveries),
          ];
        },
      ),
    );

    return pdf;
  }

  static pw.Widget _buildDeliveryTable(List<dynamic> deliveries) {
    // Calculer les totaux
    double totalValeurColis = deliveries.fold(
      0.0,
      (sum, delivery) => sum + (delivery.resteAPayer ?? 0),
    );
    double totalFraisLivraison = deliveries.fold(
      0.0,
      (sum, delivery) => sum + (delivery.fraisLivraison ?? 0),
    );
    double totalPourHCP = totalValeurColis - totalFraisLivraison;

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Tableau principal
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.black, width: 1),
          columnWidths: {
            0: const pw.FlexColumnWidth(3), // Numéro et Nom du client
            1: const pw.FlexColumnWidth(2), // Valeur du colis
            2: const pw.FlexColumnWidth(2), // Zone et prix
            3: const pw.FlexColumnWidth(2), // Montant
          },
          children: [
            // En-tête du tableau
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey300),
              children: [
                _buildDeliveryTableCell(
                  'Numéro et Nom du client',
                  isHeader: true,
                ),
                _buildDeliveryTableCell('Valeur du colis', isHeader: true),
                _buildDeliveryTableCell('Zone et prix', isHeader: true),
                _buildDeliveryTableCell('Montant', isHeader: true),
              ],
            ),
            // Lignes des livraisons
            ...deliveries.map(
              (delivery) => pw.TableRow(
                children: [
                  _buildDeliveryTableCell(
                    '${delivery.nomClient ?? 'Client'}\n${delivery.numeroClient}',
                  ),
                  _buildDeliveryTableCell(
                    _currencyFormat.format(delivery.resteAPayer ?? 0),
                  ),
                  _buildDeliveryTableCell(
                    '${delivery.zoneLivraison}\n${_currencyFormat.format(delivery.fraisLivraison ?? 0)}',
                  ),
                  _buildDeliveryTableCell(
                    _currencyFormat.format(delivery.fraisLivraison ?? 0),
                  ),
                ],
              ),
            ),
            // Ligne Total
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildDeliveryTableCell('Total', isHeader: true),
                _buildDeliveryTableCell(
                  _currencyFormat.format(totalValeurColis),
                  isHeader: true,
                ),
                _buildDeliveryTableCell(
                  _currencyFormat.format(totalFraisLivraison),
                  isHeader: true,
                ),
                _buildDeliveryTableCell(
                  _currencyFormat.format(totalPourHCP),
                  isHeader: true,
                ),
              ],
            ),
          ],
        ),
        pw.SizedBox(height: 30),
        // Résumé
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(15),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black, width: 2),
            borderRadius: pw.BorderRadius.circular(10),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'Résumé',
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Text('Nombre de colis : ${deliveries.length} colis'),
              pw.Text(
                'Valeur total des colis : ${formatMontant(totalValeurColis)} Fr CFA',
              ),
              pw.Text(
                'Frais de livraison : ${formatMontant(totalFraisLivraison)} Fr CFA',
              ),
              pw.Text(
                'Total pour HCP-DESIGN : ${formatMontant(totalPourHCP)} Fr CFA',
              ),
            ],
          ),
        ),
        pw.SizedBox(height: 20),
        // Total pour HCP-DESIGN
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(15),
          decoration: const pw.BoxDecoration(color: PdfColors.grey300),
          child: pw.Center(
            child: pw.Text(
              'Total pour HCP-DESIGN : ${formatMontant(totalPourHCP)} Fr CFA',
              style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
            ),
          ),
        ),
      ],
    );
  }

  static pw.Widget _buildDeliveryTableCell(
    String text, {
    bool isHeader = false,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: isHeader ? pw.TextAlign.center : pw.TextAlign.left,
      ),
    );
  }

  /// Génère un PDF pour un colis individuel sans le sauvegarder (pour prévisualisation)
  static Future<pw.Document> generateColisDocument(Colis colis) async {
    final pdf = pw.Document();

    // Charger l'image du colis si elle existe
    pw.ImageProvider? colisImage;
    try {
      if (colis.photoPath.isNotEmpty) {
        final imageFile = File(colis.photoPath);
        if (await imageFile.exists()) {
          final imageBytes = await imageFile.readAsBytes();
          colisImage = pw.MemoryImage(imageBytes);
        }
      }
    } catch (e) {
      // Si l'image n'est pas trouvée, on continue sans image
      colisImage = null;
    }

    // Charger le header
    final header = await _buildHeader();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              header,
              pw.SizedBox(height: 30),

              // Titre
              pw.Center(
                child: pw.Text(
                  'FICHE COLIS',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.blue900,
                  ),
                ),
              ),
              pw.SizedBox(height: 20),

              // Informations du colis
              pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Image du colis
                  pw.Expanded(
                    flex: 1,
                    child: pw.Container(
                      height: 200,
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.grey400),
                        borderRadius: pw.BorderRadius.circular(8),
                      ),
                      child:
                          colisImage != null
                              ? pw.Image(colisImage, fit: pw.BoxFit.cover)
                              : pw.Center(
                                child: pw.Column(
                                  mainAxisAlignment:
                                      pw.MainAxisAlignment.center,
                                  children: [
                                    pw.Icon(
                                      pw.IconData(0xe1c4), // Icons.inventory_2
                                      size: 48,
                                      color: PdfColors.grey400,
                                    ),
                                    pw.SizedBox(height: 8),
                                    pw.Text(
                                      'Aucune image',
                                      style: pw.TextStyle(
                                        color: PdfColors.grey600,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                    ),
                  ),
                  pw.SizedBox(width: 20),

                  // Détails du colis
                  pw.Expanded(
                    flex: 2,
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        _buildColisInfoRow('Libellé', colis.libelle),
                        _buildColisInfoRow('Numéro client', colis.numeroClient),
                        _buildColisInfoRow(
                          'Nom client',
                          colis.nomClient ?? 'Non renseigné',
                        ),
                        _buildColisInfoRow(
                          'Zone de livraison',
                          colis.zoneLivraison,
                        ),
                        _buildColisInfoRow(
                          'Adresse',
                          colis.adresseLivraison ?? 'Non renseignée',
                        ),
                        _buildColisInfoRow('Statut', colis.statut.libelle),
                        _buildColisInfoRow(
                          'Date d\'ajout',
                          _dateFormat.format(colis.dateAjout),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              pw.SizedBox(height: 30),

              // Informations financières
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey100,
                  borderRadius: pw.BorderRadius.circular(8),
                  border: pw.Border.all(color: PdfColors.grey300),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Informations financières',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.blue900,
                      ),
                    ),
                    pw.SizedBox(height: 12),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          'Reste à payer:',
                          style: const pw.TextStyle(fontSize: 14),
                        ),
                        pw.Text(
                          '${formatMontant(colis.resteAPayer)} FCFA',
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.green700,
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 8),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          'Frais de livraison:',
                          style: const pw.TextStyle(fontSize: 14),
                        ),
                        pw.Text(
                          '${formatMontant(colis.fraisLivraison)} FCFA',
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.orange700,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Notes si présentes
              if (colis.notes != null && colis.notes!.isNotEmpty)
                pw.SizedBox(height: 20),
              if (colis.notes != null && colis.notes!.isNotEmpty)
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(16),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.blue50,
                    borderRadius: pw.BorderRadius.circular(8),
                    border: pw.Border.all(color: PdfColors.blue200),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'Notes',
                        style: pw.TextStyle(
                          fontSize: 14,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.blue900,
                        ),
                      ),
                      pw.SizedBox(height: 8),
                      pw.Text(
                        colis.notes!,
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),

              pw.Spacer(),

              // Footer
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(12),
                decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                child: pw.Center(
                  child: pw.Text(
                    'Généré le ${_dateFormat.format(DateTime.now())} - HCP-DESIGN',
                    style: pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );

    return pdf;
  }

  static pw.Widget _buildColisInfoRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 8),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.SizedBox(
            width: 120,
            child: pw.Text(
              '$label:',
              style: pw.TextStyle(
                fontSize: 12,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.grey700,
              ),
            ),
          ),
          pw.Expanded(
            child: pw.Text(value, style: const pw.TextStyle(fontSize: 12)),
          ),
        ],
      ),
    );
  }

  /// Génère une mini facture 5x3 pouces pour colis
  static Future<pw.Document> generateMiniInvoice({
    required String nomClient,
    required String numeroClient,
    required String lieuLivraison,
    required double resteAPayer,
    String? qrCoqueData,
    String? referenceFacture,
    String? produitArticle,
    DateTime? dateLivraison,
  }) async {
    final pdf = pw.Document();

    // Format exact 3 pouces largeur x 5 pouces hauteur (7,6 x 12,7 cm) - Portrait
    const pageFormat = PdfPageFormat(
      3 * PdfPageFormat.inch,
      5 * PdfPageFormat.inch,
      marginAll: 6,
    );

    // Charger les images QR codes
    final qrImages = await _loadQRImages();
    final logoImage = await _loadLogo();

    pdf.addPage(
      pw.Page(
        pageFormat: pageFormat,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              // En-tête avec logo HCP DESIGN et QR codes (optimisé)
              _buildOptimizedMiniHeader(logoImage, qrImages, referenceFacture),

              pw.SizedBox(height: 12), // Espacement augmenté
              // Informations client (optimisé)
              _buildOptimizedMiniClientInfo(
                nomClient,
                numeroClient,
                lieuLivraison,
                resteAPayer,
                produitArticle,
                dateLivraison,
              ),

              // Spacer pour pousser les QR codes vers le bas
              pw.Spacer(),

              // Section paiement QR codes (optimisé) - maintenant en bas
              _buildOptimizedMiniPaymentSection(qrImages),
            ],
          );
        },
      ),
    );

    return pdf;
  }

  /// Construit l'en-tête optimisé de la mini facture
  static pw.Widget _buildOptimizedMiniHeader(
    pw.MemoryImage? logoImage,
    Map<String, pw.MemoryImage?> qrImages,
    String? referenceFacture,
  ) {
    return pw.Column(
      children: [
        // Référence de facture (si fournie)
        if (referenceFacture != null && referenceFacture.isNotEmpty)
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.only(bottom: 1),
            child: pw.Align(
              alignment: pw.Alignment.centerRight,
              child: pw.Text(
                'Réf: $referenceFacture',
                style: pw.TextStyle(
                  fontSize: 7, // Taille réduite
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
          ),

        // En-tête principal optimisé pour portrait
        pw.Container(
          width: double.infinity,
          height: 40, // Hauteur augmentée pour mieux remplir
          child: pw.Row(
            children: [
              // QR code 1 (site)
              pw.Expanded(
                flex: 1,
                child: pw.Container(
                  margin: const pw.EdgeInsets.all(2),
                  child: pw.Center(
                    child:
                        qrImages['site'] != null
                            ? pw.Image(
                              qrImages['site']!,
                              fit: pw.BoxFit.contain,
                            )
                            : pw.Text('QR', style: pw.TextStyle(fontSize: 7)),
                  ),
                ),
              ),

              // Section logo HCP DESIGN (au centre)
              pw.Expanded(
                flex: 2,
                child: pw.Container(
                  padding: const pw.EdgeInsets.all(3),
                  child: pw.Center(
                    child:
                        logoImage != null
                            ? pw.Image(logoImage, fit: pw.BoxFit.contain)
                            : pw.Text(
                              'HCP\nDESIGN',
                              style: pw.TextStyle(
                                fontSize: 10, // Augmenté pour format portrait
                                fontWeight: pw.FontWeight.bold,
                              ),
                              textAlign: pw.TextAlign.center,
                            ),
                  ),
                ),
              ),

              // QR code 2 (whatsapp)
              pw.Expanded(
                flex: 1,
                child: pw.Container(
                  margin: const pw.EdgeInsets.all(2),
                  child: pw.Center(
                    child:
                        qrImages['whatsapp'] != null
                            ? pw.Image(
                              qrImages['whatsapp']!,
                              fit: pw.BoxFit.contain,
                            )
                            : pw.Text('QR', style: pw.TextStyle(fontSize: 7)),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Construit la section informations client optimisée
  static pw.Widget _buildOptimizedMiniClientInfo(
    String nomClient,
    String numeroClient,
    String lieuLivraison,
    double resteAPayer,
    String? produitArticle,
    DateTime? dateLivraison,
  ) {
    return pw.Column(
      children: [
        // Produit/Article (si fourni et pas de date pour économiser l'espace)
        if (produitArticle != null &&
            produitArticle.isNotEmpty &&
            dateLivraison == null) ...[
          _buildOptimizedInfoField('Produit / Article', produitArticle),
          pw.SizedBox(height: 8),
        ],

        _buildOptimizedInfoField('Nom du client', nomClient),
        pw.SizedBox(height: 8), // Espacement augmenté
        _buildOptimizedPhoneField('Numéro du client', numeroClient),
        pw.SizedBox(height: 8), // Espacement augmenté
        _buildOptimizedInfoField('Lieu de livraison', lieuLivraison),
        pw.SizedBox(height: 8), // Espacement augmenté
        // Date de livraison (si fournie) - priorité sur le produit
        if (dateLivraison != null) ...[
          _buildOptimizedInfoField('Date', _dateFormat.format(dateLivraison)),
          pw.SizedBox(height: 8), // Espacement augmenté
        ],

        _buildOptimizedAmountField(
          'Reste à payer',
          '${formatMontant(resteAPayer)} FCFA',
        ),
      ],
    );
  }

  /// Construit un champ d'information optimisé
  static pw.Widget _buildOptimizedInfoField(String title, String value) {
    return pw.Column(
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.normal),
          textAlign: pw.TextAlign.center,
        ),
        pw.SizedBox(height: 3),
        pw.Container(
          width:
              180, // Largeur augmentée pour mieux remplir la feuille 3 pouces
          padding: const pw.EdgeInsets.only(bottom: 2),
          child: pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 11,
              fontWeight: pw.FontWeight.bold,
            ), // Données en gras et taille augmentée
            textAlign: pw.TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// Construit un champ d'information optimisé avec une taille de police plus grande (pour le montant)
  static pw.Widget _buildOptimizedAmountField(String title, String value) {
    return pw.Column(
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.normal),
          textAlign: pw.TextAlign.center,
        ),
        pw.SizedBox(height: 3),
        pw.Container(
          width:
              180, // Largeur augmentée pour mieux remplir la feuille 3 pouces
          padding: const pw.EdgeInsets.only(bottom: 2),

          child: pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 16, // Taille encore plus augmentée pour le montant
              fontWeight: pw.FontWeight.bold,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// Construit un champ d'information optimisé avec une taille de police plus grande (pour le numéro de téléphone)
  static pw.Widget _buildOptimizedPhoneField(String title, String value) {
    return pw.Column(
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.normal),
          textAlign: pw.TextAlign.center,
        ),
        pw.SizedBox(height: 3),
        pw.Container(
          width:
              180, // Largeur augmentée pour mieux remplir la feuille 3 pouces
          padding: const pw.EdgeInsets.only(bottom: 2),

          child: pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 13, // Taille augmentée pour le numéro de téléphone
              fontWeight: pw.FontWeight.bold,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// Construit la section paiement optimisée
  static pw.Widget _buildOptimizedMiniPaymentSection(
    Map<String, pw.MemoryImage?> qrImages,
  ) {
    return pw.Column(
      children: [
        pw.Text(
          'QR de paiement',
          style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold),
          textAlign: pw.TextAlign.center,
        ),
        pw.SizedBox(height: 4),
        pw.Container(
          width: double.infinity,
          height: 60, // Hauteur augmentée pour format portrait
          child: pw.Row(
            children: [
              // QR Wave
              pw.Expanded(
                child: pw.Container(
                  child: pw.Column(
                    mainAxisAlignment: pw.MainAxisAlignment.center,
                    children: [
                      pw.Text(
                        'Wave',
                        style: pw.TextStyle(
                          fontSize: 9,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 3),
                      pw.Expanded(
                        child: pw.Container(
                          margin: const pw.EdgeInsets.all(3),
                          child:
                              qrImages['wave'] != null
                                  ? pw.Image(
                                    qrImages['wave']!,
                                    fit: pw.BoxFit.contain,
                                  )
                                  : pw.Container(
                                    decoration: pw.BoxDecoration(
                                      border: pw.Border.all(
                                        color: PdfColors.grey,
                                      ),
                                    ),
                                    child: pw.Center(
                                      child: pw.Text(
                                        'QR',
                                        style: pw.TextStyle(fontSize: 6),
                                      ),
                                    ),
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // QR Orange Money
              pw.Expanded(
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    pw.Text(
                      'Orange',
                      style: pw.TextStyle(
                        fontSize: 9,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 3),
                    pw.Expanded(
                      child: pw.Container(
                        margin: const pw.EdgeInsets.all(3),
                        child:
                            qrImages['orange'] != null
                                ? pw.Image(
                                  qrImages['orange']!,
                                  fit: pw.BoxFit.contain,
                                )
                                : pw.Container(
                                  decoration: pw.BoxDecoration(
                                    border: pw.Border.all(
                                      color: PdfColors.grey,
                                    ),
                                  ),
                                  child: pw.Center(
                                    child: pw.Text(
                                      'QR',
                                      style: pw.TextStyle(fontSize: 6),
                                    ),
                                  ),
                                ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Charge les images QR codes depuis les assets
  static Future<Map<String, pw.MemoryImage?>> _loadQRImages() async {
    final Map<String, pw.MemoryImage?> qrImages = {};

    final qrPaths = {
      'site': 'assets/qr_codes/QR_Site/qr_site.png',
      'whatsapp': 'assets/qr_codes/QR_WhatsApp/qr_whatsapp.png',
      'wave': 'assets/qr_codes/QR_Paiement_Wave/qr_wave.png',
      'orange': 'assets/qr_codes/QR_Paiement_Orange/qr_orange.png',
    };

    for (final entry in qrPaths.entries) {
      try {
        final ByteData data = await rootBundle.load(entry.value);
        final Uint8List bytes = data.buffer.asUint8List();
        qrImages[entry.key] = pw.MemoryImage(bytes);
      } catch (e) {
        // QR code non trouvé, on continue sans
        qrImages[entry.key] = null;
      }
    }

    return qrImages;
  }
}
